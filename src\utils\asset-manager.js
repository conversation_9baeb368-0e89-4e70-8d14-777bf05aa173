const fs = require('fs-extra');
const path = require('path');
const https = require('https');
const { app } = require('electron');
const http = require('http');
const { URL } = require('url');

/**
 * Asset Manager
 * Handles asset loading, caching, and fallback mechanisms
 */
class AssetManager {
    constructor() {
        this.assetCache = new Map();
        this.downloadCache = new Map();
        this.assetsDir = path.join(__dirname, '../../assets');
        this.cacheDir = path.join(app.getPath('userData'), 'cache', 'assets');
        this.placeholderDir = path.join(this.assetsDir, 'placeholders');
        
        this.ensureDirectories();
        this.loadPlaceholderAssets();
    }

    ensureDirectories() {
        try {
            fs.ensureDirSync(this.cacheDir);
            fs.ensureDirSync(this.placeholderDir);
        } catch (error) {
            console.error('Failed to create asset directories:', error);
        }
    }

    loadPlaceholderAssets() {
        // Define default placeholder assets
        this.placeholders = {
            thumbnail: path.join(this.assetsDir, 'placeholder-thumbnail.png'),
            icon: path.join(this.assetsDir, 'icon.png'),
            avatar: path.join(this.placeholderDir, 'avatar.png'),
            video: path.join(this.placeholderDir, 'video.png'),
            audio: path.join(this.placeholderDir, 'audio.png'),
            error: path.join(this.placeholderDir, 'error.png'),
            loading: path.join(this.placeholderDir, 'loading.gif')
        };

        // Create missing placeholder assets
        this.createMissingPlaceholders();
    }

    createMissingPlaceholders() {
        // Create simple placeholder assets if they don't exist
        const placeholderConfigs = {
            'avatar.png': this.createAvatarPlaceholder,
            'video.png': this.createVideoPlaceholder,
            'audio.png': this.createAudioPlaceholder,
            'error.png': this.createErrorPlaceholder
        };

        for (const [filename, createFunc] of Object.entries(placeholderConfigs)) {
            const filePath = path.join(this.placeholderDir, filename);
            if (!fs.existsSync(filePath)) {
                try {
                    createFunc.call(this, filePath);
                } catch (error) {
                    console.error(`Failed to create placeholder ${filename}:`, error);
                }
            }
        }
    }

    // Simple placeholder creation methods (these would create basic images)
    createAvatarPlaceholder(filePath) {
        // For now, copy the main icon as avatar placeholder
        const iconPath = path.join(this.assetsDir, 'icon.png');
        if (fs.existsSync(iconPath)) {
            fs.copySync(iconPath, filePath);
        }
    }

    createVideoPlaceholder(filePath) {
        // Copy thumbnail placeholder as video placeholder
        const thumbnailPath = path.join(this.assetsDir, 'placeholder-thumbnail.png');
        if (fs.existsSync(thumbnailPath)) {
            fs.copySync(thumbnailPath, filePath);
        }
    }

    createAudioPlaceholder(filePath) {
        // Copy thumbnail placeholder as audio placeholder
        const thumbnailPath = path.join(this.assetsDir, 'placeholder-thumbnail.png');
        if (fs.existsSync(thumbnailPath)) {
            fs.copySync(thumbnailPath, filePath);
        }
    }

    createErrorPlaceholder(filePath) {
        // Copy thumbnail placeholder as error placeholder
        const thumbnailPath = path.join(this.assetsDir, 'placeholder-thumbnail.png');
        if (fs.existsSync(thumbnailPath)) {
            fs.copySync(thumbnailPath, filePath);
        }
    }

    /**
     * Get asset with fallback mechanism
     * @param {string} assetPath - Path to the asset
     * @param {string} fallbackType - Type of fallback ('thumbnail', 'icon', etc.)
     * @returns {string} - Path to the asset or fallback
     */
    getAssetWithFallback(assetPath, fallbackType = 'thumbnail') {
        // Check if asset exists
        if (assetPath && fs.existsSync(assetPath)) {
            return assetPath;
        }

        // Return appropriate fallback
        const fallbackPath = this.placeholders[fallbackType];
        if (fallbackPath && fs.existsSync(fallbackPath)) {
            return fallbackPath;
        }

        // Ultimate fallback - return the main thumbnail placeholder
        const ultimateFallback = this.placeholders.thumbnail;
        if (ultimateFallback && fs.existsSync(ultimateFallback)) {
            return ultimateFallback;
        }

        // If even that doesn't exist, return null
        console.warn('No fallback asset available for type:', fallbackType);
        return null;
    }

    /**
     * Download and cache remote asset (like thumbnails)
     * @param {string} url - URL of the asset to download
     * @param {string} filename - Local filename to save as
     * @param {string} fallbackType - Fallback type if download fails
     * @returns {Promise<string>} - Path to the downloaded or fallback asset
     */
    async downloadAndCacheAsset(url, filename, fallbackType = 'thumbnail') {
        try {
            // Check if already cached
            const cachedPath = path.join(this.cacheDir, filename);
            if (fs.existsSync(cachedPath)) {
                return cachedPath;
            }

            // Check if download is already in progress
            if (this.downloadCache.has(url)) {
                return await this.downloadCache.get(url);
            }

            // Start download
            const downloadPromise = this.downloadAsset(url, cachedPath);
            this.downloadCache.set(url, downloadPromise);

            const result = await downloadPromise;
            this.downloadCache.delete(url);

            return result;

        } catch (error) {
            console.error('Failed to download asset:', error);
            this.downloadCache.delete(url);
            return this.getAssetWithFallback(null, fallbackType);
        }
    }

    /**
     * Download asset from URL
     * @param {string} url - URL to download from
     * @param {string} outputPath - Path to save the file
     * @returns {Promise<string>} - Path to the downloaded file
     */
    downloadAsset(url, outputPath) {
        return new Promise((resolve, reject) => {
            try {
                const urlObj = new URL(url);
                const client = urlObj.protocol === 'https:' ? https : http;

                const request = client.get(url, (response) => {
                    // Check for successful response
                    if (response.statusCode !== 200) {
                        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                        return;
                    }

                    // Check content type
                    const contentType = response.headers['content-type'];
                    if (!contentType || !contentType.startsWith('image/')) {
                        reject(new Error(`Invalid content type: ${contentType}`));
                        return;
                    }

                    // Create write stream
                    const writeStream = fs.createWriteStream(outputPath);
                    
                    response.pipe(writeStream);

                    writeStream.on('finish', () => {
                        writeStream.close();
                        resolve(outputPath);
                    });

                    writeStream.on('error', (error) => {
                        fs.unlink(outputPath, () => {}); // Clean up partial file
                        reject(error);
                    });
                });

                request.on('error', (error) => {
                    reject(error);
                });

                // Set timeout
                request.setTimeout(30000, () => {
                    request.abort();
                    reject(new Error('Download timeout'));
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Get thumbnail with smart fallback
     * @param {string} thumbnailUrl - URL of the thumbnail
     * @param {string} videoId - Unique identifier for caching
     * @param {string} videoType - Type of video ('video' or 'audio')
     * @returns {Promise<string>} - Path to thumbnail or fallback
     */
    async getThumbnail(thumbnailUrl, videoId, videoType = 'video') {
        if (!thumbnailUrl) {
            return this.getAssetWithFallback(null, videoType);
        }

        try {
            // Generate cache filename
            const extension = this.getExtensionFromUrl(thumbnailUrl) || '.jpg';
            const filename = `thumb_${videoId}${extension}`;

            return await this.downloadAndCacheAsset(thumbnailUrl, filename, videoType);
        } catch (error) {
            console.error('Failed to get thumbnail:', error);
            return this.getAssetWithFallback(null, videoType);
        }
    }

    /**
     * Get platform icon with fallback
     * @param {string} platform - Platform name
     * @returns {string} - Path to platform icon or fallback
     */
    getPlatformIcon(platform) {
        const platformIconPath = path.join(this.assetsDir, 'platforms', `${platform}.png`);
        return this.getAssetWithFallback(platformIconPath, 'icon');
    }

    /**
     * Clean up old cached assets
     * @param {number} maxAgeMs - Maximum age in milliseconds
     */
    async cleanupCache(maxAgeMs = 7 * 24 * 60 * 60 * 1000) { // Default: 7 days
        try {
            if (!fs.existsSync(this.cacheDir)) {
                return;
            }

            const files = await fs.readdir(this.cacheDir);
            const now = Date.now();

            for (const file of files) {
                const filePath = path.join(this.cacheDir, file);
                try {
                    const stats = await fs.stat(filePath);
                    const age = now - stats.mtime.getTime();
                    
                    if (age > maxAgeMs) {
                        await fs.unlink(filePath);
                        console.log(`Cleaned up old cached asset: ${file}`);
                    }
                } catch (error) {
                    console.error(`Error cleaning up cached asset ${file}:`, error);
                }
            }
        } catch (error) {
            console.error('Error during cache cleanup:', error);
        }
    }

    /**
     * Get file extension from URL
     * @param {string} url - URL to extract extension from
     * @returns {string|null} - File extension or null
     */
    getExtensionFromUrl(url) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            const extension = path.extname(pathname);
            return extension || null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Get cache statistics
     * @returns {Object} - Cache statistics
     */
    async getCacheStats() {
        try {
            const files = await fs.readdir(this.cacheDir);
            let totalSize = 0;

            for (const file of files) {
                const filePath = path.join(this.cacheDir, file);
                const stats = await fs.stat(filePath);
                totalSize += stats.size;
            }

            return {
                fileCount: files.length,
                totalSize: totalSize,
                cacheDir: this.cacheDir
            };
        } catch (error) {
            return {
                fileCount: 0,
                totalSize: 0,
                cacheDir: this.cacheDir,
                error: error.message
            };
        }
    }
}

// Export singleton instance
module.exports = new AssetManager();
