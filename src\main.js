const { app, BrowserWindow, ipc<PERSON>ain, dialog, shell, Menu } = require('electron');
const path = require('path');
const fs = require('fs-extra');
const os = require('os');

// Enhanced error logging for packaged apps
const logError = (context, error) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${context}] ${error.message}\n${error.stack}\n`;
  console.error(logMessage);

  // Also write to a log file for packaged apps
  try {
    const userDataPath = app.getPath('userData');
    const logPath = path.join(userDataPath, 'error.log');
    fs.appendFileSync(logPath, logMessage);
    console.log(`Error logged to: ${logPath}`);
  } catch (logWriteError) {
    console.error('Failed to write to log file:', logWriteError);
  }
};

// Enhanced startup logging
const logStartup = (message) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [STARTUP] ${message}\n`;
  console.log(logMessage);

  try {
    const userDataPath = app.getPath('userData');
    const logPath = path.join(userDataPath, 'startup.log');
    fs.appendFileSync(logPath, logMessage);
  } catch (error) {
    console.error('Failed to write startup log:', error);
  }
};

// Global error handlers
process.on('uncaughtException', (error) => {
  logError('UncaughtException', error);
  try {
    dialog.showErrorBox('Application Error', `An unexpected error occurred: ${error.message}\n\nCheck the logs in your user data folder for more details.`);
  } catch (dialogError) {
    console.error('Failed to show error dialog:', dialogError);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  logError('UnhandledRejection', error);
});

logStartup('Starting Downloader Pro...');
logStartup(`Process arguments: ${process.argv.join(' ')}`);
logStartup(`Working directory: ${process.cwd()}`);
logStartup(`App path: ${app.getAppPath()}`);
logStartup(`User data path: ${app.getPath('userData')}`);

// Load modules with error handling
let DownloadEngine, tempManager, assetManager;

try {
  logStartup('Loading DownloadEngine...');
  DownloadEngine = require('./main/download-engine');
  logStartup('✅ DownloadEngine loaded');
} catch (error) {
  logError('DownloadEngine-Load', error);
  logStartup(`❌ Failed to load DownloadEngine: ${error.message}`);
  throw error;
}

try {
  logStartup('Loading tempManager...');
  tempManager = require('./utils/temp-manager');
  logStartup('✅ tempManager loaded');
} catch (error) {
  logError('tempManager-Load', error);
  logStartup(`❌ Failed to load tempManager: ${error.message}`);
  throw error;
}

try {
  logStartup('Loading assetManager...');
  assetManager = require('./utils/asset-manager');
  logStartup('✅ assetManager loaded');
} catch (error) {
  logError('assetManager-Load', error);
  logStartup(`❌ Failed to load assetManager: ${error.message}`);
  throw error;
}

// Auto-updater with error handling
let autoUpdater = null;
try {
  logStartup('Loading electron-updater...');
  autoUpdater = require('electron-updater').autoUpdater;

  // Disable auto-updater for standalone builds to prevent crashes
  autoUpdater.autoDownload = false;
  autoUpdater.autoInstallOnAppQuit = false;

  logStartup('✅ electron-updater loaded');
} catch (error) {
  logStartup(`❌ Auto-updater not available: ${error.message}`);
}

// Keep a global reference of the window object
let mainWindow;
let isDev = process.argv.includes('--dev');
let downloadEngine;

// Enable live reload for development
if (isDev) {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

function createWindow() {
  try {
    console.log('Creating main window...');

    // Check if preload file exists
    const preloadPath = path.join(__dirname, 'preload.js');
    if (!fs.existsSync(preloadPath)) {
      throw new Error(`Preload file not found: ${preloadPath}`);
    }

    // Check if renderer HTML exists
    const rendererPath = path.join(__dirname, 'renderer', 'index.html');
    if (!fs.existsSync(rendererPath)) {
      throw new Error(`Renderer HTML not found: ${rendererPath}`);
    }

    // Check if icon exists
    const iconPath = path.join(__dirname, '..', 'assets', 'icon.png');
    const iconExists = fs.existsSync(iconPath);
    if (!iconExists) {
      console.warn(`Icon not found: ${iconPath}`);
    }

    // Create the browser window
    mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: preloadPath
      },
      icon: iconExists ? iconPath : undefined,
      show: false,
      titleBarStyle: 'default',
      frame: true
    });

    console.log('Window created, loading renderer...');

    // Load the app
    mainWindow.loadFile(rendererPath);

    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
      console.log('Window ready to show');
      mainWindow.show();

      if (isDev) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window closed
    mainWindow.on('closed', () => {
      console.log('Main window closed');
      mainWindow = null;
    });

    // Handle renderer process crashes
    mainWindow.webContents.on('crashed', (event, killed) => {
      logError('RendererCrash', new Error(`Renderer process crashed. Killed: ${killed}`));
    });

    // Handle unresponsive renderer
    mainWindow.on('unresponsive', () => {
      logError('RendererUnresponsive', new Error('Renderer process became unresponsive'));
    });

    console.log('✅ Window setup complete');

  } catch (error) {
    logError('CreateWindow', error);
    throw error;
  }

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });
}

// Create application menu
function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Download',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-download');
          }
        },
        { type: 'separator' },
        {
          label: 'Settings',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About',
          click: () => {
            mainWindow.webContents.send('menu-about');
          }
        },
        {
          label: 'Check for Updates',
          click: () => {
            if (autoUpdater) {
              autoUpdater.checkForUpdatesAndNotify();
            } else {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'Updates',
                message: 'Auto-updater is not available in this build.',
                detail: 'Please check the website for manual updates.'
              });
            }
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(async () => {
  try {
    console.log('App ready, initializing...');

    console.log('Creating window...');
    createWindow();

    console.log('Creating menu...');
    createMenu();

    console.log('Initializing download engine...');
    downloadEngine = new DownloadEngine();
    setupDownloadEngineListeners();
    console.log('✅ Download engine initialized');

    // Auto updater - disabled for standalone builds
    if (!isDev && autoUpdater && false) { // Temporarily disabled
      try {
        logStartup('Checking for updates...');
        autoUpdater.checkForUpdatesAndNotify();
      } catch (error) {
        logStartup(`Auto-updater check failed: ${error.message}`);
      }
    }

    console.log('✅ App initialization complete');

  } catch (error) {
    logError('AppReady', error);

    // Show error dialog and exit
    dialog.showErrorBox(
      'Startup Error',
      `Failed to initialize application: ${error.message}\n\nCheck the error log for details.`
    );
    app.quit();
  }
}).catch(error => {
  logError('AppReadyPromise', error);
  app.quit();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Cleanup before app quits
app.on('before-quit', async (event) => {
  console.log('App is quitting, performing cleanup...');

  try {
    // Prevent immediate quit to allow cleanup
    event.preventDefault();

    // Cleanup download engine
    if (downloadEngine) {
      await downloadEngine.cleanupAll();
    }

    // Cleanup temp manager
    await tempManager.cleanupAll();

    console.log('Cleanup completed, quitting app...');

    // Now actually quit
    app.exit(0);
  } catch (error) {
    console.error('Error during cleanup:', error);
    app.exit(1);
  }
});

// Handle app activation (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Setup download engine event listeners
function setupDownloadEngineListeners() {
  downloadEngine.on('download-started', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-started', data);
    }
  });

  downloadEngine.on('download-progress', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-progress', data);
    }
  });

  downloadEngine.on('download-complete', async (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-complete', data);

      // Add to download history
      try {
        const historyData = {
          id: data.id,
          title: data.options?.videoInfo?.title || 'Unknown Title',
          thumbnail: data.options?.videoInfo?.thumbnail,
          url: data.options?.url,
          filePath: data.filePath,
          size: data.options?.videoInfo?.filesize || 0,
          quality: data.options?.quality,
          format: data.options?.format,
          duration: Date.now() - (data.startTime || Date.now())
        };

        const historyPath = path.join(app.getPath('userData'), 'download-history.json');
        let history = { downloads: [] };

        if (await fs.pathExists(historyPath)) {
          history = await fs.readJson(historyPath);
        }

        if (!history.downloads) {
          history.downloads = [];
        }

        // Add new download to history
        history.downloads.unshift({
          ...historyData,
          completedAt: new Date().toISOString()
        });

        // Keep only last 100 downloads
        if (history.downloads.length > 100) {
          history.downloads = history.downloads.slice(0, 100);
        }

        await fs.writeJson(historyPath, history, { spaces: 2 });
      } catch (error) {
        console.error('Failed to add download to history:', error);
      }
    }
  });

  downloadEngine.on('download-error', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-error', data);
    }
  });

  downloadEngine.on('download-paused', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-paused', data);
    }
  });

  downloadEngine.on('download-resumed', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-resumed', data);
    }
  });

  downloadEngine.on('download-cancelled', (data) => {
    if (mainWindow) {
      mainWindow.webContents.send('download-cancelled', data);
    }
  });
}

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

// File operations
ipcMain.handle('show-in-folder', async (event, filePath) => {
  try {
    if (await fs.pathExists(filePath)) {
      shell.showItemInFolder(filePath);
      return true;
    } else {
      throw new Error('File not found');
    }
  } catch (error) {
    console.error('Failed to show file in folder:', error);
    throw error;
  }
});

ipcMain.handle('open-file', async (event, filePath) => {
  try {
    if (await fs.pathExists(filePath)) {
      await shell.openPath(filePath);
      return true;
    } else {
      throw new Error('File not found');
    }
  } catch (error) {
    console.error('Failed to open file:', error);
    throw error;
  }
});

ipcMain.handle('get-platform', () => {
  return process.platform;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('get-downloads-path', () => {
  return app.getPath('downloads');
});

ipcMain.handle('get-videos-path', () => {
  return app.getPath('videos');
});

ipcMain.handle('show-item-in-folder', (event, fullPath) => {
  shell.showItemInFolder(fullPath);
});

ipcMain.handle('open-external', (event, url) => {
  shell.openExternal(url);
});

// Clipboard operations
ipcMain.handle('clipboard-read-text', () => {
  const { clipboard } = require('electron');
  return clipboard.readText();
});

ipcMain.handle('clipboard-write-text', (event, text) => {
  const { clipboard } = require('electron');
  clipboard.writeText(text);
});

// Settings management
ipcMain.handle('get-settings', async () => {
  const settingsPath = path.join(app.getPath('userData'), 'settings.json');
  try {
    if (await fs.pathExists(settingsPath)) {
      return await fs.readJson(settingsPath);
    }
  } catch (error) {
    console.error('Failed to read settings:', error);
  }
  return {};
});

ipcMain.handle('save-settings', async (event, settings) => {
  const settingsPath = path.join(app.getPath('userData'), 'settings.json');
  try {
    await fs.writeJson(settingsPath, settings, { spaces: 2 });
    return true;
  } catch (error) {
    console.error('Failed to save settings:', error);
    throw error;
  }
});

// Download operations
// Get yt-dlp status information
ipcMain.handle('get-ytdlp-status', async () => {
  try {
    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }
    return await downloadEngine.getYtDlpStatus();
  } catch (error) {
    console.error('Failed to get yt-dlp status:', error);
    return {
      available: false,
      version: null,
      path: null,
      error: error.message,
      installInstructions: [
        'Failed to check yt-dlp status',
        'Please ensure yt-dlp is properly installed'
      ]
    };
  }
});

ipcMain.handle('get-video-info', async (event, url) => {
  try {
    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }

    // Check if yt-dlp is available
    const isAvailable = await downloadEngine.checkYtDlpAvailability();
    if (!isAvailable) {
      // Provide proper error handling instead of mock data
      throw new Error('yt-dlp is not available. Please install yt-dlp to download videos.\n\nTo install yt-dlp:\n1. Run "npm run install-ytdlp" in the project directory\n2. Or install manually from https://github.com/yt-dlp/yt-dlp\n3. Restart the application after installation');
    }

    // Try to get real video info
    try {
      const videoInfo = await downloadEngine.getVideoInfo(url);

      // Validate the returned data
      if (!videoInfo || typeof videoInfo !== 'object') {
        throw new Error('Invalid video information received');
      }

      // Process thumbnail with asset manager
      const videoId = videoInfo.id || 'unknown';
      const videoType = videoInfo.acodec && !videoInfo.vcodec ? 'audio' : 'video';
      let thumbnailPath;

      try {
        thumbnailPath = await assetManager.getThumbnail(videoInfo.thumbnail, videoId, videoType);
      } catch (error) {
        console.error('Failed to process thumbnail:', error);
        thumbnailPath = assetManager.getAssetWithFallback(null, videoType);
      }

      // Ensure required fields are present with fallbacks
      return {
        title: videoInfo.title || 'Unknown Title',
        description: videoInfo.description || 'No description available',
        thumbnail: thumbnailPath,
        duration: videoInfo.duration || 0,
        uploader: videoInfo.uploader || 'Unknown Channel',
        view_count: videoInfo.view_count || 0,
        upload_date: videoInfo.upload_date || null,
        formats: Array.isArray(videoInfo.formats) ? videoInfo.formats : [],
        webpage_url: videoInfo.webpage_url || url,
        id: videoId,
        extractor: videoInfo.extractor || 'unknown',
        // Additional metadata
        like_count: videoInfo.like_count || null,
        dislike_count: videoInfo.dislike_count || null,
        comment_count: videoInfo.comment_count || null,
        tags: Array.isArray(videoInfo.tags) ? videoInfo.tags : [],
        categories: Array.isArray(videoInfo.categories) ? videoInfo.categories : [],
        // Quality information
        width: videoInfo.width || null,
        height: videoInfo.height || null,
        fps: videoInfo.fps || null,
        vcodec: videoInfo.vcodec || null,
        acodec: videoInfo.acodec || null,
        filesize: videoInfo.filesize || null,
        filesize_approx: videoInfo.filesize_approx || null,
        // Asset information
        thumbnailOriginal: videoInfo.thumbnail,
        thumbnailCached: thumbnailPath,
        videoType: videoType
      };
    } catch (infoError) {
      // If getting video info fails, provide detailed error information
      console.error('Failed to get video info:', infoError);

      // Try to provide helpful error messages based on the error
      let errorMessage = 'Failed to get video information';

      if (infoError.message.includes('Unsupported URL')) {
        errorMessage = 'This URL is not supported. Please check that the URL is correct and from a supported platform.';
      } else if (infoError.message.includes('Video unavailable')) {
        errorMessage = 'This video is unavailable. It may be private, deleted, or restricted in your region.';
      } else if (infoError.message.includes('network')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (infoError.message.includes('timeout')) {
        errorMessage = 'Request timed out. The server may be slow or unavailable. Please try again later.';
      } else if (infoError.message.includes('403') || infoError.message.includes('Forbidden')) {
        errorMessage = 'Access forbidden. This video may be restricted or require authentication.';
      } else if (infoError.message.includes('404') || infoError.message.includes('Not Found')) {
        errorMessage = 'Video not found. The URL may be incorrect or the video may have been removed.';
      } else if (infoError.message.includes('429') || infoError.message.includes('rate limit')) {
        errorMessage = 'Rate limited. Please wait a moment before trying again.';
      }

      throw new Error(`${errorMessage}\n\nOriginal error: ${infoError.message}`);
    }
  } catch (error) {
    console.error('Failed to get video info:', error);
    throw error;
  }
});

ipcMain.handle('start-download', async (event, options) => {
  try {
    console.log('Main process received download options:', JSON.stringify(options, null, 2));

    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }

    const downloadId = Date.now().toString();

    // Check if yt-dlp is available
    const isAvailable = await downloadEngine.checkYtDlpAvailability();
    console.log('yt-dlp available:', isAvailable);

    if (!isAvailable) {
      // Simulate download for demo purposes
      setTimeout(() => {
        mainWindow.webContents.send('download-started', {
          id: downloadId,
          size: 50000000 // 50MB
        });
      }, 100);

      // Simulate download progress
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress >= 100) {
          progress = 100;
          clearInterval(progressInterval);

          mainWindow.webContents.send('download-complete', {
            id: downloadId,
            filePath: path.join(options.destination, 'sample-video.mp4')
          });
        } else {
          mainWindow.webContents.send('download-progress', {
            id: downloadId,
            progress: progress,
            speed: Math.random() * 1000000,
            downloaded: (progress / 100) * 50000000,
            size: 50000000
          });
        }
      }, 1000);

      return downloadId;
    }

    // Use real download engine
    return await downloadEngine.startDownload(downloadId, options);
  } catch (error) {
    console.error('Failed to start download:', error);
    throw error;
  }
});

ipcMain.handle('pause-download', async (event, downloadId) => {
  try {
    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }
    return downloadEngine.pauseDownload(downloadId);
  } catch (error) {
    console.error('Failed to pause download:', error);
    throw error;
  }
});

ipcMain.handle('resume-download', async (event, downloadId) => {
  try {
    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }
    return downloadEngine.resumeDownload(downloadId);
  } catch (error) {
    console.error('Failed to resume download:', error);
    throw error;
  }
});

ipcMain.handle('cancel-download', async (event, downloadId) => {
  try {
    if (!downloadEngine) {
      throw new Error('Download engine not initialized');
    }
    return downloadEngine.cancelDownload(downloadId);
  } catch (error) {
    console.error('Failed to cancel download:', error);
    throw error;
  }
});

// Download history management
ipcMain.handle('get-download-history', async () => {
  try {
    const historyPath = path.join(app.getPath('userData'), 'download-history.json');
    if (await fs.pathExists(historyPath)) {
      const history = await fs.readJson(historyPath);
      return history.downloads || [];
    }
    return [];
  } catch (error) {
    console.error('Failed to get download history:', error);
    return [];
  }
});

ipcMain.handle('add-to-history', async (event, downloadData) => {
  try {
    const historyPath = path.join(app.getPath('userData'), 'download-history.json');
    let history = { downloads: [] };

    if (await fs.pathExists(historyPath)) {
      history = await fs.readJson(historyPath);
    }

    if (!history.downloads) {
      history.downloads = [];
    }

    // Add new download to history
    history.downloads.unshift({
      ...downloadData,
      completedAt: new Date().toISOString()
    });

    // Keep only last 100 downloads
    if (history.downloads.length > 100) {
      history.downloads = history.downloads.slice(0, 100);
    }

    await fs.writeJson(historyPath, history, { spaces: 2 });
    return true;
  } catch (error) {
    console.error('Failed to add to download history:', error);
    throw error;
  }
});

ipcMain.handle('clear-download-history', async () => {
  try {
    const historyPath = path.join(app.getPath('userData'), 'download-history.json');
    await fs.writeJson(historyPath, { downloads: [] }, { spaces: 2 });
    return true;
  } catch (error) {
    console.error('Failed to clear download history:', error);
    throw error;
  }
});

// Asset management operations
ipcMain.handle('get-platform-icon', async (event, platform) => {
  try {
    return assetManager.getPlatformIcon(platform);
  } catch (error) {
    console.error('Failed to get platform icon:', error);
    return assetManager.getAssetWithFallback(null, 'icon');
  }
});

ipcMain.handle('get-asset-with-fallback', async (event, assetPath, fallbackType) => {
  try {
    return assetManager.getAssetWithFallback(assetPath, fallbackType);
  } catch (error) {
    console.error('Failed to get asset with fallback:', error);
    return null;
  }
});

ipcMain.handle('cleanup-asset-cache', async () => {
  try {
    await assetManager.cleanupCache();
    return true;
  } catch (error) {
    console.error('Failed to cleanup asset cache:', error);
    throw error;
  }
});

ipcMain.handle('get-cache-stats', async () => {
  try {
    return await assetManager.getCacheStats();
  } catch (error) {
    console.error('Failed to get cache stats:', error);
    return {
      fileCount: 0,
      totalSize: 0,
      error: error.message
    };
  }
});

// Theme management
ipcMain.handle('get-theme', async () => {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    if (await fs.pathExists(settingsPath)) {
      const settings = await fs.readJson(settingsPath);
      return settings.theme || 'light';
    }
    return 'light';
  } catch (error) {
    console.error('Failed to get theme:', error);
    return 'light';
  }
});

ipcMain.handle('set-theme', async (event, theme) => {
  try {
    const settingsPath = path.join(app.getPath('userData'), 'settings.json');
    let settings = {};

    if (await fs.pathExists(settingsPath)) {
      settings = await fs.readJson(settingsPath);
    }

    settings.theme = theme;
    await fs.writeJson(settingsPath, settings, { spaces: 2 });

    // Notify renderer of theme change
    if (mainWindow) {
      mainWindow.webContents.send('theme-changed', theme);
    }

    return true;
  } catch (error) {
    console.error('Failed to set theme:', error);
    throw error;
  }
});

// Auto updater events (only if autoUpdater is available)
if (autoUpdater) {
  autoUpdater.on('checking-for-update', () => {
    console.log('Checking for update...');
  });

  autoUpdater.on('update-available', (info) => {
    console.log('Update available.');
  });

  autoUpdater.on('update-not-available', (info) => {
    console.log('Update not available.');
  });

  autoUpdater.on('error', (err) => {
    console.log('Error in auto-updater. ' + err);
  });

  autoUpdater.on('download-progress', (progressObj) => {
    let log_message = "Download speed: " + progressObj.bytesPerSecond;
    log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
    log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
    console.log(log_message);
  });

  autoUpdater.on('update-downloaded', (info) => {
    console.log('Update downloaded');
    autoUpdater.quitAndInstall();
  });
}
