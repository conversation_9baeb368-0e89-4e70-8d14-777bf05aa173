# Instagram Authentication Fix

## Problem
When trying to download Instagram Reels, users encountered the error:
```
Failed to load video info: <PERSON>rror invoking remote method 'get-video-info': Error: Failed to get video information

Original error: ERROR: [Instagram] DMlEuTN0Em: Restricted Video: You must be 18 years old or over to see this video. --cookies-from-browser or --cookies for the authentication.
```

## Root Cause
The application had a "Use browser cookies" setting in the UI, but it wasn't being implemented in the download engine. Instagram requires authentication for many videos, especially age-restricted content.

## Solution Implemented

### 1. Enhanced Download Engine (`src/main/download-engine.js`)
- **Cookie Support**: Added `--cookies-from-browser chrome` argument when `useCookies` is enabled
- **Instagram-Specific Configuration**: Added Instagram-specific options including:
  - API version specification: `--extractor-args instagram:api_version=v1`
  - User agent spoofing to avoid detection
- **Enhanced Error Handling**: Added `parseInstagramError()` method to provide specific error messages and solutions

### 2. Settings Integration
- **Frontend**: Modified `startDownload()` in `src/renderer/js/app.js` to pass `useCookies` setting
- **Backend**: Updated `getVideoInfo()` and `startDownload()` methods to accept and use the `useCookies` parameter
- **IPC Communication**: Updated main process handlers to pass cookie settings through the entire chain

### 3. Improved Error Messages
- **Specific Instagram Errors**: Added detection for authentication, private content, rate limiting, and network issues
- **User-Friendly Solutions**: Provide actionable solutions like "Enable 'Use browser cookies' in Settings"
- **Platform-Specific Guidance**: Different error handling for Instagram vs other platforms

## How to Use

### For Users:
1. **Enable Cookie Support**: Go to Settings → Privacy & Security → Enable "Use browser cookies"
2. **Login to Instagram**: Make sure you're logged into Instagram in your Chrome browser
3. **Try Download Again**: The app will now use your browser's Instagram cookies for authentication

### For Age-Restricted Content:
- Ensure you're logged into Instagram in your browser
- Make sure your Instagram account meets age requirements
- Enable the "Use browser cookies" setting before attempting download

## Technical Details

### Cookie Extraction
- Uses `--cookies-from-browser chrome` to extract cookies from Chrome browser
- Falls back to other browsers automatically if Chrome is not available
- Only extracts cookies when the setting is explicitly enabled by the user

### Instagram-Specific Handling
- Detects Instagram URLs and applies platform-specific configurations
- Uses appropriate user agent strings to avoid bot detection
- Implements proper error parsing for Instagram-specific error messages

### Security Considerations
- Cookies are only used when explicitly enabled by the user
- No cookies are stored or transmitted - they're extracted directly by yt-dlp
- User has full control over when cookie authentication is used

## Error Types Handled
1. **Authentication Required**: Login/cookie-related errors
2. **Private Content**: Content not accessible to current user
3. **Rate Limiting**: Too many requests from IP/account
4. **Network Issues**: Connection problems
5. **Content Not Found**: Deleted or invalid URLs

## Troubleshooting Instagram Issues

### If you still get "Instagram sent an empty response" error:

1. **Check Browser Login Status**:
   - Open Chrome and go to instagram.com
   - Make sure you're fully logged in (not just remembered)
   - Try accessing the specific Instagram post in your browser first

2. **Try Different Browsers**:
   - The app now tries Chrome, Firefox, Edge, and Safari automatically
   - Make sure you're logged into Instagram in at least one of these browsers

3. **Clear Browser Cache**:
   - Sometimes stale cookies cause issues
   - Clear Instagram cookies in your browser and log in again

4. **Check Instagram URL Format**:
   - Make sure you're using the full Instagram URL
   - Example: `https://www.instagram.com/reel/ABC123/`
   - Avoid shortened URLs or mobile app links

5. **Account Restrictions**:
   - Some content requires specific account permissions
   - Age-restricted content needs an 18+ account
   - Private accounts need follow permissions

### Enhanced Error Messages
The app now provides specific guidance for:
- Empty responses from Instagram
- Authentication failures
- Rate limiting issues
- Private content access
- Network connectivity problems

## Testing
To test the fix:
1. Find an age-restricted Instagram Reel
2. Try downloading without cookies enabled (should fail with clear error message)
3. Enable "Use browser cookies" in settings
4. Ensure you're logged into Instagram in Chrome/Firefox/Edge
5. Try downloading again (should succeed)

## Advanced Troubleshooting
If issues persist:
1. Check the console logs in the app for detailed error messages
2. Verify yt-dlp is properly installed and updated
3. Test with a public Instagram post first
4. Ensure your Instagram account has appropriate permissions
