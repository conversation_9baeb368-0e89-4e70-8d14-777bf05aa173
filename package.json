{"name": "downloader-pro", "productName": "Downloader Pro", "version": "1.0.0", "description": "Modern desktop application for downloading content from various platforms", "main": "src/main.js", "homepage": "https://github.com/your-username/downloader-pro", "author": {"name": "Your Name", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "npm run prebuild && electron-builder", "build:win": "npm run prebuild && electron-builder --win", "build:win32": "npm run prebuild && electron-builder --win --ia32", "build:win64": "npm run prebuild && electron-builder --win --x64", "build:mac": "npm run prebuild && electron-builder --mac", "build:linux": "npm run prebuild && electron-builder --linux", "prebuild": "node scripts/prebuild.js", "postbuild": "node scripts/postbuild.js", "dist": "npm run build", "dist:win": "npm run build:win", "dist:portable": "npm run prebuild && electron-builder --win portable", "install-ytdlp": "node scripts/install-ytdlp.js", "bundle-dependencies": "node scripts/bundle-dependencies.js", "verify-build": "node scripts/verify-build.js", "postinstall": "echo \"📋 To enable full functionality, run: npm run install-ytdlp\"", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["downloader", "video", "audio", "youtube", "facebook", "instagram", "tiktok", "twitter", "electron"], "devDependencies": {"electron": "^37.2.4", "electron-builder": "^26.0.12", "electron-reload": "^2.0.0-alpha.1", "extract-zip": "^2.0.1"}, "dependencies": {"child_process": "^1.0.2", "electron-updater": "^6.6.2", "fs-extra": "^11.3.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "meilisearch": "^0.51.0", "node-fetch": "^3.3.2", "path-browserify": "^1.0.1", "util": "^0.12.5"}, "build": {"appId": "com.yourcompany.downloader-pro", "productName": "Downloader Pro", "copyright": "Copyright © 2024 ${author}", "directories": {"output": "dist", "buildResources": "build"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json", "!node_modules/.cache", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "build/resources/", "to": "resources/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "forceCodeSigning": false, "signAndEditExecutable": false, "artifactName": "${productName}-${version}-${arch}.${ext}", "extraFiles": [{"from": "build/bin/", "to": "resources/bin/", "filter": ["**/*"]}]}, "nsis": {"oneClick": false, "perMachine": false, "allowElevation": true, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": "always", "createStartMenuShortcut": true, "shortcutName": "Downloader Pro", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Multimedia", "artifactName": "${productName}-Setup-${version}-${arch}.${ext}", "license": "LICENSE"}, "portable": {"artifactName": "${productName}-Portable-${version}-${arch}.${ext}", "requestExecutionLevel": "user"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": ["AppImage", "deb"], "icon": "assets/icon.png"}, "publish": null}}