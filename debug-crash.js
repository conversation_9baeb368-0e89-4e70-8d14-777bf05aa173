// Debug script to help identify why the application crashes
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🔍 Debugging application crash...\n');

// Get user data path where logs would be stored
const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'downloader-pro');
console.log(`📁 User data path: ${userDataPath}`);

// Check if logs exist from previous runs
const errorLogPath = path.join(userDataPath, 'error.log');
const startupLogPath = path.join(userDataPath, 'startup.log');

console.log('\n📋 Checking for existing logs:');
if (fs.existsSync(errorLogPath)) {
  console.log('✅ Error log found');
  try {
    const errorLog = fs.readFileSync(errorLogPath, 'utf8');
    console.log('\n🚨 Recent errors:');
    console.log(errorLog.split('\n').slice(-20).join('\n'));
  } catch (error) {
    console.log('❌ Failed to read error log:', error.message);
  }
} else {
  console.log('❌ No error log found');
}

if (fs.existsSync(startupLogPath)) {
  console.log('✅ Startup log found');
  try {
    const startupLog = fs.readFileSync(startupLogPath, 'utf8');
    console.log('\n🚀 Recent startup events:');
    console.log(startupLog.split('\n').slice(-20).join('\n'));
  } catch (error) {
    console.log('❌ Failed to read startup log:', error.message);
  }
} else {
  console.log('❌ No startup log found');
}

// Clear old logs for fresh debugging
console.log('\n🧹 Clearing old logs for fresh debugging...');
try {
  if (fs.existsSync(errorLogPath)) fs.unlinkSync(errorLogPath);
  if (fs.existsSync(startupLogPath)) fs.unlinkSync(startupLogPath);
  console.log('✅ Old logs cleared');
} catch (error) {
  console.log('❌ Failed to clear logs:', error.message);
}

// Test if the application executable exists
const appPath = path.join(__dirname, 'dist', 'win-unpacked', 'Downloader Pro.exe');
console.log(`\n🔍 Checking for application executable: ${appPath}`);

if (!fs.existsSync(appPath)) {
  console.log('❌ Application executable not found!');
  console.log('   Please build the application first with: npm run build:win64');
  process.exit(1);
}

console.log('✅ Application executable found');

// Run the application and capture output
console.log('\n🚀 Starting application with debug output...');

const appProcess = spawn(appPath, [], {
  stdio: ['pipe', 'pipe', 'pipe'],
  cwd: path.dirname(appPath)
});

let stdout = '';
let stderr = '';

appProcess.stdout.on('data', (data) => {
  const text = data.toString();
  stdout += text;
  console.log('[STDOUT]', text.trim());
});

appProcess.stderr.on('data', (data) => {
  const text = data.toString();
  stderr += text;
  console.log('[STDERR]', text.trim());
});

appProcess.on('error', (error) => {
  console.log('\n❌ Process error:', error);
});

appProcess.on('exit', (code, signal) => {
  console.log(`\n📊 Process exited with code: ${code}, signal: ${signal}`);
  
  if (code !== 0) {
    console.log('\n🚨 Application crashed! Checking logs...');
    
    // Check for new logs
    setTimeout(() => {
      if (fs.existsSync(errorLogPath)) {
        console.log('\n📋 Error log contents:');
        try {
          const errorLog = fs.readFileSync(errorLogPath, 'utf8');
          console.log(errorLog);
        } catch (error) {
          console.log('Failed to read error log:', error.message);
        }
      }
      
      if (fs.existsSync(startupLogPath)) {
        console.log('\n📋 Startup log contents:');
        try {
          const startupLog = fs.readFileSync(startupLogPath, 'utf8');
          console.log(startupLog);
        } catch (error) {
          console.log('Failed to read startup log:', error.message);
        }
      }
      
      console.log('\n💡 Debugging suggestions:');
      console.log('1. Check if all dependencies are properly bundled');
      console.log('2. Verify that all required files exist in the dist folder');
      console.log('3. Check Windows Event Viewer for additional error details');
      console.log('4. Try running the application as administrator');
      console.log('5. Check antivirus logs for blocked files');
      
    }, 1000);
  } else {
    console.log('✅ Application exited normally');
  }
});

// Kill the process after 30 seconds if it's still running
setTimeout(() => {
  if (!appProcess.killed) {
    console.log('\n⏰ Timeout reached, killing process...');
    appProcess.kill();
  }
}, 30000);

console.log('⏳ Waiting for application to start (timeout: 30 seconds)...');
