// Download management functionality
class DownloadManager {
    constructor() {
        this.downloads = new Map();
        this.setupDownloadEventListeners();
    }

    setupDownloadEventListeners() {
        // Listen for download progress updates
        window.electronAPI.onDownloadProgress((event, data) => {
            this.updateDownloadProgress(data.id, data);
        });

        // Listen for download completion
        window.electronAPI.onDownloadComplete((event, data) => {
            this.handleDownloadComplete(data.id, data);
        });

        // Listen for download errors
        window.electronAPI.onDownloadError((event, data) => {
            this.handleDownloadError(data.id, data);
        });

        // Listen for download started
        window.electronAPI.onDownloadStarted((event, data) => {
            this.handleDownloadStarted(data.id, data);
        });

        // Listen for download paused
        window.electronAPI.onDownloadPaused((event, data) => {
            this.handleDownloadPaused(data.id, data);
        });

        // Listen for download resumed
        window.electronAPI.onDownloadResumed((event, data) => {
            this.handleDownloadResumed(data.id, data);
        });

        // Listen for download cancelled
        window.electronAPI.onDownloadCancelled((event, data) => {
            this.handleDownloadCancelled(data.id, data);
        });
    }

    addDownload(downloadId, options) {
        console.log('DownloadManager.addDownload called with:', downloadId, options);

        const download = {
            id: downloadId,
            url: options.url,
            title: options.videoInfo?.title || 'Unknown Title',
            thumbnail: options.videoInfo?.thumbnail || '',
            type: options.type,
            quality: options.quality,
            format: options.format,
            destination: options.destination,
            status: 'pending',
            progress: 0,
            speed: 0,
            size: 0,
            downloaded: 0,
            startTime: Date.now(),
            endTime: null,
            error: null
        };

        console.log('Created download object:', download);

        this.downloads.set(downloadId, download);
        console.log('Added to downloads map, total downloads:', this.downloads.size);

        this.renderDownload(download);
        this.updateEmptyState();

        console.log('Download added and rendered successfully');
    }

    renderDownload(download) {
        const downloadsList = document.getElementById('downloads-list');
        
        // Remove empty state if it exists
        const emptyState = downloadsList.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        const downloadElement = document.createElement('div');
        downloadElement.className = 'download-item';
        downloadElement.dataset.downloadId = download.id;

        downloadElement.innerHTML = `
            <img src="${download.thumbnail}" alt="Thumbnail" class="download-thumbnail" 
                 onerror="this.src='../../assets/placeholder-thumbnail.png'">
            <div class="download-info">
                <div class="download-title" title="${download.title}">${download.title}</div>
                <div class="download-meta">
                    <span>${download.type.toUpperCase()}</span>
                    <span>${download.quality}</span>
                    <span>${download.format.toUpperCase()}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${download.progress}%"></div>
                </div>
                <div class="download-status">
                    <div class="status-indicator status-${this.getStatusClass(download.status)}"></div>
                    <span class="status-text">${this.getStatusText(download)}</span>
                </div>
            </div>
            <div class="download-controls">
                ${this.getControlButtons(download)}
            </div>
        `;

        downloadsList.appendChild(downloadElement);
    }

    updateDownload(downloadId, updates) {
        const download = this.downloads.get(downloadId);
        if (!download) return;

        Object.assign(download, updates);
        this.downloads.set(downloadId, download);

        const downloadElement = document.querySelector(`[data-download-id="${downloadId}"]`);
        if (downloadElement) {
            this.updateDownloadElement(downloadElement, download);
        }
    }

    updateDownloadElement(element, download) {
        // Update progress bar
        const progressFill = element.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${download.progress}%`;
        }

        // Update status indicator
        const statusIndicator = element.querySelector('.status-indicator');
        if (statusIndicator) {
            statusIndicator.className = `status-indicator status-${this.getStatusClass(download.status)}`;
        }

        // Update status text
        const statusText = element.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = this.getStatusText(download);
        }

        // Update control buttons
        const controlsContainer = element.querySelector('.download-controls');
        if (controlsContainer) {
            controlsContainer.innerHTML = this.getControlButtons(download);
        }
    }

    getStatusClass(status) {
        switch (status) {
            case 'downloading':
            case 'resuming':
                return 'downloading';
            case 'completed':
                return 'completed';
            case 'error':
                return 'error';
            case 'paused':
            case 'pausing':
                return 'paused';
            case 'pending':
            case 'cancelling':
                return 'downloading';
            default:
                return 'downloading';
        }
    }

    getStatusText(download) {
        switch (download.status) {
            case 'pending':
                return 'Preparing...';
            case 'downloading':
                const progressText = `${Math.round(download.progress || 0)}%`;
                const speedText = download.speed > 0 ? this.formatSpeed(download.speed) : 'Calculating...';
                const sizeText = download.size > 0 ?
                    `${this.formatSize(download.downloaded || 0)} / ${this.formatSize(download.size)}` :
                    (download.downloaded > 0 ? this.formatSize(download.downloaded) : 'Unknown size');

                // Calculate ETA
                const etaText = this.calculateETA(download);
                return `${progressText} • ${speedText} • ${sizeText}${etaText ? ' • ' + etaText : ''}`;
            case 'completed':
                const completedSize = download.size > 0 ? this.formatSize(download.size) : 'Unknown size';
                const duration = download.endTime && download.startTime ?
                    this.formatDuration(download.endTime - download.startTime) : 'Unknown time';
                return `Completed • ${completedSize} • ${duration}`;
            case 'error':
                const errorText = `Error: ${download.error || 'Unknown error'}`;
                const solutionText = download.solution ? ` • Solution: ${download.solution}` : '';
                return errorText + solutionText;
            case 'paused':
                const pausedProgress = `${Math.round(download.progress || 0)}%`;
                const pausedSize = download.size > 0 ?
                    `${this.formatSize(download.downloaded || 0)} / ${this.formatSize(download.size)}` :
                    (download.downloaded > 0 ? this.formatSize(download.downloaded) : '');
                return `Paused • ${pausedProgress}${pausedSize ? ' • ' + pausedSize : ''}`;
            case 'pausing':
                return 'Pausing...';
            case 'resuming':
                return 'Resuming...';
            case 'cancelling':
                return 'Cancelling...';
            default:
                return 'Unknown status';
        }
    }

    getControlButtons(download) {
        switch (download.status) {
            case 'downloading':
                return `
                    <button class="control-button" onclick="downloadManager.pauseDownload('${download.id}')" title="Pause">
                        ⏸️
                    </button>
                    <button class="control-button" onclick="downloadManager.cancelDownload('${download.id}')" title="Cancel">
                        ❌
                    </button>
                `;
            case 'paused':
                return `
                    <button class="control-button" onclick="downloadManager.resumeDownload('${download.id}')" title="Resume">
                        ▶️
                    </button>
                    <button class="control-button" onclick="downloadManager.cancelDownload('${download.id}')" title="Cancel">
                        ❌
                    </button>
                `;
            case 'completed':
                return `
                    <button class="control-button" onclick="downloadManager.showInFolder('${download.id}')" title="Show in folder">
                        📁
                    </button>
                    <button class="control-button" onclick="downloadManager.removeDownload('${download.id}')" title="Remove from list">
                        🗑️
                    </button>
                `;
            case 'error':
                const fixButton = download.errorType === 'encoder' || download.errorType === 'conversion'
                    ? `<button class="control-button fix-button" onclick="downloadManager.showErrorSolution('${download.id}')" title="Fix Issue">
                        🔧
                    </button>`
                    : '';
                return `
                    ${fixButton}
                    <button class="control-button" onclick="downloadManager.retryDownload('${download.id}')" title="Retry">
                        🔄
                    </button>
                    <button class="control-button" onclick="downloadManager.removeDownload('${download.id}')" title="Remove from list">
                        🗑️
                    </button>
                `;
            default:
                return '';
        }
    }

    // Event handlers for download lifecycle
    handleDownloadStarted(downloadId, data) {
        this.updateDownload(downloadId, {
            status: 'downloading',
            size: data.size || 0
        });
    }

    updateDownloadProgress(downloadId, data) {
        const download = this.downloads.get(downloadId);
        if (!download) return;

        // Don't update progress if download is in a transitional state
        if (['pausing', 'resuming', 'cancelling'].includes(download.status)) {
            return;
        }

        // Ensure progress is within valid range
        const progress = Math.min(Math.max(Math.round(data.progress || 0), 0), 100);

        // Update size if we get a larger value (yt-dlp sometimes provides better estimates later)
        const newSize = data.size || 0;
        const currentSize = download.size || 0;
        const size = Math.max(newSize, currentSize);

        // Calculate downloaded bytes based on progress and size
        let downloaded = data.downloaded || 0;
        if (size > 0 && progress > 0) {
            downloaded = Math.max(downloaded, (progress / 100) * size);
        }

        this.updateDownload(downloadId, {
            status: 'downloading',
            progress: progress,
            speed: data.speed || 0,
            downloaded: downloaded,
            size: size
        });

        // Send smart notification for progress milestones
        if (window.app && window.app.notifyDownloadProgress) {
            const eta = data.speed > 0 ? (size - downloaded) / data.speed : 0;
            window.app.notifyDownloadProgress(downloadId, progress, data.speed || 0, eta);
        }

        // Log progress for debugging
        console.log(`Download ${downloadId}: ${progress}% - ${this.formatSpeed(data.speed || 0)} - ${this.formatSize(downloaded)}/${this.formatSize(size)}`);
    }

    handleDownloadComplete(downloadId, data) {
        this.updateDownload(downloadId, {
            status: 'completed',
            progress: 100,
            endTime: Date.now(),
            filePath: data.filePath
        });

        // Show smart notification
        const download = this.downloads.get(downloadId);
        if (download && window.app && window.app.notifyDownloadComplete) {
            window.app.notifyDownloadComplete({
                id: downloadId,
                title: download.title,
                size: download.size,
                path: data.filePath
            });
        }
    }

    handleDownloadError(downloadId, data) {
        const errorMessage = data.error || 'Unknown error';
        console.log('Processing download error:', errorMessage);

        const processedError = this.processDownloadError(errorMessage);
        console.log('Processed error:', processedError);

        this.updateDownload(downloadId, {
            status: 'error',
            error: processedError.message,
            errorType: processedError.type,
            solution: processedError.solution
        });

        // Show smart notification with solution
        const download = this.downloads.get(downloadId);
        if (download && window.app && typeof window.app.notifyDownloadError === 'function') {
            window.app.notifyDownloadError(processedError.message, {
                id: downloadId,
                title: download.title,
                solution: processedError.solution,
                errorType: processedError.type
            });
        } else {
            console.warn('Smart notification not available, showing basic notification');
            if (window.app && typeof window.app.showNotification === 'function') {
                window.app.showNotification(`Download failed: ${processedError.message}`, 'error');
            }
        }
    }

    processDownloadError(errorMessage) {
        const error = errorMessage.toLowerCase();

        // FFmpeg/Encoder related errors
        if (error.includes('encoder not found') || error.includes('postprocessing')) {
            return {
                type: 'encoder',
                message: 'FFmpeg encoder not found',
                solution: 'Install FFmpeg or try downloading without format conversion'
            };
        }

        if (error.includes('error opening output files')) {
            return {
                type: 'file_access',
                message: 'Cannot create output file',
                solution: 'Check folder permissions and available disk space'
            };
        }

        if (error.includes('ffmpeg') || error.includes('conversion failed')) {
            return {
                type: 'conversion',
                message: 'Format conversion failed',
                solution: 'Try downloading in original format or install FFmpeg'
            };
        }

        if (error.includes('network') || error.includes('connection')) {
            return {
                type: 'network',
                message: 'Network connection error',
                solution: 'Check your internet connection and try again'
            };
        }

        if (error.includes('unavailable') || error.includes('not found')) {
            return {
                type: 'content',
                message: 'Video not available',
                solution: 'The video may be private, deleted, or region-blocked'
            };
        }

        // Instagram-specific authentication errors
        if (error.includes('instagram') && (error.includes('login') || error.includes('authentication') ||
            error.includes('restricted video') || error.includes('cookies'))) {
            return {
                type: 'authentication',
                message: 'Instagram authentication required',
                solution: 'Enable "Use browser cookies" in Settings and make sure you\'re logged into Instagram in your browser'
            };
        }

        // Default case
        return {
            type: 'general',
            message: errorMessage,
            solution: 'Try again or contact support if the problem persists'
        };
    }

    handleDownloadPaused(downloadId, data) {
        this.updateDownload(downloadId, {
            status: 'paused'
        });
    }

    handleDownloadResumed(downloadId, data) {
        this.updateDownload(downloadId, {
            status: 'downloading'
        });
    }

    handleDownloadCancelled(downloadId, data) {
        // Remove the download from the list
        this.removeDownload(downloadId);
    }

    // Control methods
    async pauseDownload(downloadId) {
        try {
            const download = this.downloads.get(downloadId);
            if (!download) return;

            // Immediately update UI to show pausing state
            this.updateDownload(downloadId, { status: 'pausing' });

            const result = await window.electronAPI.pauseDownload(downloadId);
            if (result) {
                // The actual status update will come from the event handler
                console.log('Download pause requested successfully');
            } else {
                // Revert status if pause failed
                this.updateDownload(downloadId, { status: 'downloading' });
                window.app.showNotification('Failed to pause download', 'error');
            }
        } catch (error) {
            console.error('Failed to pause download:', error);
            // Revert status if pause failed
            this.updateDownload(downloadId, { status: 'downloading' });
            window.app.showNotification('Failed to pause download', 'error');
        }
    }

    async resumeDownload(downloadId) {
        try {
            const download = this.downloads.get(downloadId);
            if (!download) return;

            // Immediately update UI to show resuming state
            this.updateDownload(downloadId, { status: 'resuming' });

            const result = await window.electronAPI.resumeDownload(downloadId);
            if (result) {
                // The actual status update will come from the event handler
                console.log('Download resume requested successfully');
            } else {
                // Revert status if resume failed
                this.updateDownload(downloadId, { status: 'paused' });
                window.app.showNotification('Failed to resume download', 'error');
            }
        } catch (error) {
            console.error('Failed to resume download:', error);
            // Revert status if resume failed
            this.updateDownload(downloadId, { status: 'paused' });
            window.app.showNotification('Failed to resume download', 'error');
        }
    }

    async cancelDownload(downloadId) {
        try {
            const download = this.downloads.get(downloadId);
            if (!download) return;

            // Show confirmation dialog for active downloads
            if (download.status === 'downloading' || download.status === 'paused') {
                const confirmed = confirm(`Are you sure you want to cancel "${download.title}"?`);
                if (!confirmed) return;
            }

            // Immediately update UI to show cancelling state
            this.updateDownload(downloadId, { status: 'cancelling' });

            const result = await window.electronAPI.cancelDownload(downloadId);
            if (result) {
                // The actual removal will come from the event handler
                console.log('Download cancellation requested successfully');
            } else {
                // Revert status if cancel failed
                this.updateDownload(downloadId, { status: download.status });
                window.app.showNotification('Failed to cancel download', 'error');
            }
        } catch (error) {
            console.error('Failed to cancel download:', error);
            window.app.showNotification('Failed to cancel download', 'error');
        }
    }

    async retryDownload(downloadId) {
        const download = this.downloads.get(downloadId);
        if (!download) return;

        try {
            // Reset download state
            this.updateDownload(downloadId, {
                status: 'pending',
                progress: 0,
                downloaded: 0,
                error: null
            });

            // Restart download
            await window.electronAPI.startDownload({
                url: download.url,
                type: download.type,
                quality: download.quality,
                format: download.format,
                destination: download.destination
            });
        } catch (error) {
            console.error('Failed to retry download:', error);
            window.app.showNotification('Failed to retry download', 'error');
        }
    }

    async showInFolder(downloadId) {
        const download = this.downloads.get(downloadId);
        if (!download || !download.filePath) {
            console.error('Download not found or no file path:', { downloadId, download });
            window.app.showNotification('Download file not found', 'error');
            return;
        }

        console.log('Showing file in folder:', download.filePath);

        try {
            // Try the correct method name from preload.js
            await window.electronAPI.showItemInFolder(download.filePath);
            console.log('Successfully opened folder for:', download.filePath);
        } catch (error) {
            console.error('Failed to show file in folder:', error);

            // Try alternative method if the first one fails
            try {
                await window.electronAPI.showInFolder(download.filePath);
                console.log('Successfully opened folder using alternative method');
            } catch (altError) {
                console.error('Alternative method also failed:', altError);
                window.app.showNotification('Failed to show file in folder. File may have been moved or deleted.', 'error');
            }
        }
    }

    removeDownload(downloadId) {
        const downloadElement = document.querySelector(`[data-download-id="${downloadId}"]`);
        if (downloadElement) {
            downloadElement.remove();
        }

        this.downloads.delete(downloadId);
        this.updateEmptyState();
    }

    clearCompletedDownloads() {
        const completedDownloads = Array.from(this.downloads.entries())
            .filter(([id, download]) => download.status === 'completed')
            .map(([id]) => id);

        completedDownloads.forEach(id => this.removeDownload(id));

        if (completedDownloads.length > 0) {
            window.app.showNotification(`Cleared ${completedDownloads.length} completed downloads`, 'info');
        }
    }

    pauseAllDownloads() {
        const activeDownloads = Array.from(this.downloads.entries())
            .filter(([id, download]) => download.status === 'downloading')
            .map(([id]) => id);

        activeDownloads.forEach(id => this.pauseDownload(id));

        if (activeDownloads.length > 0) {
            window.app.showNotification(`Paused ${activeDownloads.length} downloads`, 'info');
        }
    }

    updateEmptyState() {
        const downloadsList = document.getElementById('downloads-list');
        const hasDownloads = downloadsList.querySelector('.download-item');

        if (!hasDownloads && !downloadsList.querySelector('.empty-state')) {
            downloadsList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📥</div>
                    <h3>No downloads yet</h3>
                    <p>Paste a video URL above to get started</p>
                </div>
            `;
        }
    }

    // Utility methods
    formatSpeed(bytesPerSecond) {
        if (!bytesPerSecond) return '0 B/s';
        
        const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        let size = bytesPerSecond;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    formatSize(bytes) {
        if (!bytes) return '0 B';
        
        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    formatDuration(milliseconds) {
        return FormatHelpers.formatDuration(Math.floor(milliseconds / 1000));
    }

    calculateETA(download) {
        if (!download.speed || download.speed <= 0 || !download.size || download.size <= 0) {
            return null;
        }

        const remainingBytes = download.size - (download.downloaded || 0);
        if (remainingBytes <= 0) {
            return null;
        }

        return FormatHelpers.formatETA(remainingBytes, download.speed);
    }

    showErrorSolution(downloadId) {
        const download = this.downloads.get(downloadId);
        if (!download || !download.solution) return;

        const modal = document.createElement('div');
        modal.className = 'modal error-solution-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🔧 Fix Download Issue</h2>
                    <button class="modal-close" onclick="this.closest('.modal').remove()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="error-details">
                        <h3>Error Details:</h3>
                        <p class="error-message">${download.error}</p>

                        <h3>Solution:</h3>
                        <p class="solution-text">${download.solution}</p>

                        ${this.getDetailedSolution(download.errorType)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="this.closest('.modal').remove()">Close</button>
                    <button class="btn-primary" onclick="downloadManager.retryWithOriginalFormat('${downloadId}')">
                        Try Original Format
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.classList.remove('hidden');
    }

    getDetailedSolution(errorType) {
        switch (errorType) {
            case 'encoder':
            case 'conversion':
                return `
                    <div class="detailed-solution">
                        <h4>How to fix this:</h4>
                        <ol>
                            <li><strong>Option 1:</strong> Download in original format (no conversion needed)</li>
                            <li><strong>Option 2:</strong> Install FFmpeg:
                                <ul>
                                    <li>Windows: Download from <a href="https://ffmpeg.org/download.html" target="_blank">ffmpeg.org</a></li>
                                    <li>Mac: Run <code>brew install ffmpeg</code></li>
                                    <li>Linux: Run <code>sudo apt install ffmpeg</code></li>
                                </ul>
                            </li>
                            <li><strong>Option 3:</strong> Use audio-only download if you only need the audio</li>
                        </ol>
                        <div class="tip">
                            💡 <strong>Tip:</strong> Original format downloads are faster and don't require conversion!
                        </div>
                    </div>
                `;
            case 'file_access':
                return `
                    <div class="detailed-solution">
                        <h4>How to fix this:</h4>
                        <ol>
                            <li>Check if you have enough disk space</li>
                            <li>Ensure the download folder has write permissions</li>
                            <li>Try changing the download destination</li>
                            <li>Close any programs that might be using the file</li>
                        </ol>
                    </div>
                `;
            case 'network':
                return `
                    <div class="detailed-solution">
                        <h4>How to fix this:</h4>
                        <ol>
                            <li>Check your internet connection</li>
                            <li>Try again in a few minutes</li>
                            <li>Check if the website is accessible</li>
                            <li>Disable VPN if you're using one</li>
                        </ol>
                    </div>
                `;
            default:
                return '';
        }
    }

    async retryWithOriginalFormat(downloadId) {
        const download = this.downloads.get(downloadId);
        if (!download) return;

        try {
            // Close the modal
            document.querySelector('.error-solution-modal')?.remove();

            // Reset download state
            this.updateDownload(downloadId, {
                status: 'pending',
                progress: 0,
                downloaded: 0,
                error: null,
                errorType: null,
                solution: null
            });

            // Restart download with original format (no conversion)
            await window.electronAPI.startDownload({
                url: download.url,
                type: download.type,
                quality: download.quality,
                format: 'original', // Force original format
                destination: download.destination,
                noConversion: true // Flag to skip post-processing
            });

            window.app.showNotification('Retrying download in original format...', 'info');
        } catch (error) {
            console.error('Failed to retry download:', error);
            window.app.showNotification('Failed to retry download', 'error');
        }
    }

    // Test function to simulate encoder error (for debugging)
    testEncoderError() {
        const testDownloadId = 'test-encoder-error';

        // Add a test download
        this.downloads.set(testDownloadId, {
            id: testDownloadId,
            url: 'https://example.com/test-video',
            title: 'Test Video - Encoder Error',
            type: 'video',
            quality: '720p',
            status: 'downloading',
            progress: 50,
            destination: 'Downloads'
        });

        // Simulate encoder error
        this.handleDownloadError(testDownloadId, {
            error: 'ERROR: Postprocessing: Error opening output files: Encoder not found'
        });

        console.log('Test encoder error simulated. Check the downloads section for the error handling.');
    }

    // Test function for completed download with show in folder
    testCompletedDownload() {
        const testDownloadId = 'test-completed-' + Date.now();

        // Add a test download
        this.downloads.set(testDownloadId, {
            id: testDownloadId,
            url: 'https://example.com/test-video',
            title: 'Test Completed Video',
            type: 'video',
            quality: '720p',
            status: 'downloading',
            progress: 90,
            destination: 'Downloads'
        });

        // Update the display first
        this.updateDownloadDisplay();

        // Simulate completion after a short delay
        setTimeout(() => {
            this.handleDownloadComplete(testDownloadId, {
                filePath: 'C:\\Users\\<USER>\\Downloads\\test-video.mp4'
            });
        }, 1000);

        console.log('Test completed download created with ID:', testDownloadId);
        return testDownloadId;
    }

    // Debug function to check download data
    debugDownload(downloadId) {
        const download = this.downloads.get(downloadId);
        console.log('Download debug info:', {
            id: downloadId,
            exists: !!download,
            download: download,
            filePath: download?.filePath,
            status: download?.status
        });
        return download;
    }
}

// Initialize download manager
const downloadManager = new DownloadManager();

// Make downloadManager globally available
window.downloadManager = downloadManager;

// Function to setup the integration with the main app
function setupAppIntegration() {
    console.log('Setting up app integration...');
    console.log('window.app exists:', !!window.app);

    if (window.app) {
        window.app.addDownloadToList = (downloadId, options) => {
            console.log('App calling downloadManager.addDownload');
            downloadManager.addDownload(downloadId, options);
        };

        window.app.clearCompletedDownloads = () => {
            downloadManager.clearCompletedDownloads();
        };

        window.app.pauseAllDownloads = () => {
            downloadManager.pauseAllDownloads();
        };

        console.log('Download manager integrated with main app successfully');
        return true;
    } else {
        console.log('window.app not available yet');
        return false;
    }
}

// Listen for the app-ready event
window.addEventListener('app-ready', () => {
    console.log('App ready event received');
    setupAppIntegration();
});

// Wait for DOM to be ready and then setup integration
document.addEventListener('DOMContentLoaded', () => {
    console.log('Downloads.js DOM ready');
    // Give the app.js a moment to initialize
    setTimeout(() => {
        if (!setupAppIntegration()) {
            // If still not ready, try a few more times
            let attempts = 0;
            const retryInterval = setInterval(() => {
                attempts++;
                if (setupAppIntegration() || attempts >= 10) {
                    clearInterval(retryInterval);
                    if (attempts >= 10) {
                        console.error('Failed to integrate with main app after 10 attempts');
                    }
                }
            }, 100);
        }
    }, 50);
});

// Also try to setup immediately in case everything is already loaded
if (document.readyState === 'complete') {
    setupAppIntegration();
}
