directories:
  output: dist
  buildResources: build
appId: com.yourcompany.downloader-pro
productName: Downloader Pro
copyright: Copyright © 2024 ${author}
files:
  - filter:
      - src/**/*
      - assets/**/*
      - node_modules/**/*
      - package.json
      - '!node_modules/.cache'
      - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!**/node_modules/*.d.ts'
      - '!**/node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
extraResources:
  - from: build/resources/
    to: resources/
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
    - target: portable
      arch:
        - x64
        - ia32
  verifyUpdateCodeSignature: false
  requestedExecutionLevel: asInvoker
  forceCodeSigning: false
  signAndEditExecutable: false
  artifactName: ${productName}-${version}-${arch}.${ext}
  extraFiles:
    - from: build/bin/
      to: resources/bin/
      filter:
        - '**/*'
nsis:
  oneClick: false
  perMachine: false
  allowElevation: true
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: always
  createStartMenuShortcut: true
  shortcutName: Downloader Pro
  deleteAppDataOnUninstall: false
  runAfterFinish: true
  menuCategory: Multimedia
  artifactName: ${productName}-Setup-${version}-${arch}.${ext}
  license: LICENSE
portable:
  artifactName: ${productName}-Portable-${version}-${arch}.${ext}
  requestExecutionLevel: user
mac:
  target: dmg
  icon: assets/icon.icns
linux:
  target:
    - AppImage
    - deb
  icon: assets/icon.png
publish: null
electronVersion: 37.2.4
